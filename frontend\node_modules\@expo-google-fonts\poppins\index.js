/// Generated by expo-google-fonts/generator
/// Do not edit by hand unless you know what you are doing
///

export { useFonts } from './useFonts';

export { default as __metadata__ } from './metadata.json';
export const Poppins_100Thin = require('./Poppins_100Thin.ttf');
export const Poppins_100Thin_Italic = require('./Poppins_100Thin_Italic.ttf');
export const Poppins_200ExtraLight = require('./Poppins_200ExtraLight.ttf');
export const Poppins_200ExtraLight_Italic = require('./Poppins_200ExtraLight_Italic.ttf');
export const Poppins_300Light = require('./Poppins_300Light.ttf');
export const Poppins_300Light_Italic = require('./Poppins_300Light_Italic.ttf');
export const Poppins_400Regular = require('./Poppins_400Regular.ttf');
export const Poppins_400Regular_Italic = require('./Poppins_400Regular_Italic.ttf');
export const Poppins_500Medium = require('./Poppins_500Medium.ttf');
export const Poppins_500Medium_Italic = require('./Poppins_500Medium_Italic.ttf');
export const Poppins_600SemiBold = require('./Poppins_600SemiBold.ttf');
export const Poppins_600SemiBold_Italic = require('./Poppins_600SemiBold_Italic.ttf');
export const Poppins_700Bold = require('./Poppins_700Bold.ttf');
export const Poppins_700Bold_Italic = require('./Poppins_700Bold_Italic.ttf');
export const Poppins_800ExtraBold = require('./Poppins_800ExtraBold.ttf');
export const Poppins_800ExtraBold_Italic = require('./Poppins_800ExtraBold_Italic.ttf');
export const Poppins_900Black = require('./Poppins_900Black.ttf');
export const Poppins_900Black_Italic = require('./Poppins_900Black_Italic.ttf');
